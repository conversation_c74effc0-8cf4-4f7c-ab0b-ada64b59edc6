#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模型导出对话框模块

提供YOLO模型导出为其他格式（ONNX、TensorRT等）的用户界面
"""

import os
import sys
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    from PyQt4.QtGui import *
    from PyQt4.QtCore import *

# 导入项目模块
from libs.stringBundle import StringBundle
from libs.settings import Settings
from libs.constants import *

# 导入YOLO相关库
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False

# 设置日志
logger = logging.getLogger(__name__)


class ExportConfig:
    """导出配置类"""
    
    def __init__(self):
        self.model_path = ""
        self.export_format = "onnx"
        self.output_dir = ""
        self.output_name = ""
        
        # ONNX参数
        self.onnx_opset = 12
        self.onnx_dynamic = False
        self.onnx_simplify = True
        
        # TensorRT参数
        self.tensorrt_precision = "fp16"
        self.tensorrt_workspace = 4
        
        # 通用参数
        self.image_size = 640
        self.batch_size = 1
        self.device = "cpu"


class ModelExportThread(QThread):
    """模型导出线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度更新
    log_message = pyqtSignal(str)            # 日志消息
    export_completed = pyqtSignal(bool, str) # 导出完成
    
    def __init__(self, config: ExportConfig):
        super().__init__()
        self.config = config
        self.is_cancelled = False
    
    def cancel(self):
        """取消导出"""
        self.is_cancelled = True
    
    def run(self):
        """执行导出"""
        try:
            self.log_message.emit("开始模型导出...")
            self.progress_updated.emit(10, "正在加载模型...")
            
            if not YOLO_AVAILABLE:
                raise Exception("ultralytics库未安装，无法进行模型导出")
            
            # 检查模型文件
            if not os.path.exists(self.config.model_path):
                raise Exception(f"模型文件不存在: {self.config.model_path}")
            
            # 加载模型
            self.log_message.emit(f"加载模型: {self.config.model_path}")
            model = YOLO(self.config.model_path)
            
            if self.is_cancelled:
                return
            
            self.progress_updated.emit(30, "正在配置导出参数...")
            
            # 准备导出参数
            export_kwargs = self._prepare_export_kwargs()
            
            if self.is_cancelled:
                return
            
            self.progress_updated.emit(50, f"正在导出为{self.config.export_format.upper()}格式...")
            
            # 执行导出
            self.log_message.emit(f"开始导出为{self.config.export_format.upper()}格式...")
            
            if self.config.export_format == "onnx":
                result = model.export(format="onnx", **export_kwargs)
            elif self.config.export_format == "tensorrt":
                result = model.export(format="engine", **export_kwargs)
            elif self.config.export_format == "coreml":
                result = model.export(format="coreml", **export_kwargs)
            elif self.config.export_format == "tflite":
                result = model.export(format="tflite", **export_kwargs)
            else:
                raise Exception(f"不支持的导出格式: {self.config.export_format}")
            
            if self.is_cancelled:
                return
            
            self.progress_updated.emit(90, "正在完成导出...")
            
            # 移动文件到指定目录（如果需要）
            if self.config.output_dir and self.config.output_name:
                self._move_exported_file(result)
            
            self.progress_updated.emit(100, "导出完成")
            self.log_message.emit(f"模型导出成功: {result}")
            self.export_completed.emit(True, f"模型导出成功: {result}")
            
        except Exception as e:
            error_msg = f"模型导出失败: {str(e)}"
            self.log_message.emit(error_msg)
            self.export_completed.emit(False, error_msg)
    
    def _prepare_export_kwargs(self) -> Dict:
        """准备导出参数"""
        kwargs = {
            "imgsz": self.config.image_size,
            "device": self.config.device,
        }
        
        if self.config.export_format == "onnx":
            kwargs.update({
                "opset": self.config.onnx_opset,
                "dynamic": self.config.onnx_dynamic,
                "simplify": self.config.onnx_simplify,
            })
        elif self.config.export_format == "tensorrt":
            kwargs.update({
                "half": self.config.tensorrt_precision == "fp16",
                "workspace": self.config.tensorrt_workspace,
            })
        
        return kwargs
    
    def _move_exported_file(self, exported_path: str):
        """移动导出的文件到指定目录"""
        try:
            if not os.path.exists(exported_path):
                return
            
            # 创建目标目录
            os.makedirs(self.config.output_dir, exist_ok=True)
            
            # 构建目标文件路径
            file_ext = Path(exported_path).suffix
            target_path = os.path.join(self.config.output_dir, f"{self.config.output_name}{file_ext}")
            
            # 移动文件
            import shutil
            shutil.move(exported_path, target_path)
            self.log_message.emit(f"文件已移动到: {target_path}")
            
        except Exception as e:
            self.log_message.emit(f"移动文件失败: {str(e)}")


class ModelExportDialog(QDialog):
    """模型导出对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.string_bundle = StringBundle.get_bundle()
        self.get_str = lambda str_id: self.string_bundle.get_string(str_id)
        
        # 加载设置
        self.settings = Settings()
        self.settings.load()
        
        # 导出线程
        self.export_thread = None
        
        self.init_ui()
        self.setup_style()
        self.load_settings()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(self.get_str('exportModelDialog'))
        self.setModal(True)
        self.resize(600, 500)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel(self.get_str('exportModelTitle'))
        title_label.setObjectName("titleLabel")
        main_layout.addWidget(title_label)
        
        # 模型选择区域
        model_group = self.create_model_selection_group()
        main_layout.addWidget(model_group)
        
        # 导出格式选择区域
        format_group = self.create_format_selection_group()
        main_layout.addWidget(format_group)
        
        # 参数配置区域
        params_group = self.create_parameters_group()
        main_layout.addWidget(params_group)
        
        # 输出设置区域
        output_group = self.create_output_group()
        main_layout.addWidget(output_group)
        
        # 进度区域（初始隐藏）
        progress_group = self.create_progress_group()
        main_layout.addWidget(progress_group)
        
        # 按钮区域
        button_layout = self.create_button_layout()
        main_layout.addLayout(button_layout)
        
        # 初始隐藏进度区域
        self.progress_group.setVisible(False)

    def create_model_selection_group(self):
        """创建模型选择区域"""
        group = QGroupBox(self.get_str('selectModel'))
        layout = QVBoxLayout(group)

        # 模型路径选择
        path_layout = QHBoxLayout()
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setPlaceholderText(self.get_str('selectModelPath'))
        self.browse_model_btn = QPushButton(self.get_str('browse'))
        self.browse_model_btn.clicked.connect(self.browse_model_file)

        path_layout.addWidget(QLabel(self.get_str('modelPath')))
        path_layout.addWidget(self.model_path_edit, 1)
        path_layout.addWidget(self.browse_model_btn)
        layout.addLayout(path_layout)

        # 模型信息显示
        self.model_info_label = QLabel(self.get_str('noModelSelected'))
        self.model_info_label.setObjectName("infoLabel")
        layout.addWidget(self.model_info_label)

        return group

    def create_format_selection_group(self):
        """创建导出格式选择区域"""
        group = QGroupBox(self.get_str('exportFormat'))
        layout = QVBoxLayout(group)

        # 格式选择
        format_layout = QHBoxLayout()
        self.format_combo = QComboBox()
        self.format_combo.addItems([
            "ONNX (.onnx)",
            "TensorRT (.engine)",
            "CoreML (.mlmodel)",
            "TensorFlow Lite (.tflite)"
        ])
        self.format_combo.currentTextChanged.connect(self.on_format_changed)

        format_layout.addWidget(QLabel(self.get_str('format')))
        format_layout.addWidget(self.format_combo, 1)
        layout.addLayout(format_layout)

        # 格式说明
        self.format_desc_label = QLabel(self.get_str('onnxDescription'))
        self.format_desc_label.setObjectName("descLabel")
        self.format_desc_label.setWordWrap(True)
        layout.addWidget(self.format_desc_label)

        return group

    def create_parameters_group(self):
        """创建参数配置区域"""
        group = QGroupBox(self.get_str('exportParameters'))
        layout = QVBoxLayout(group)

        # 创建堆叠窗口用于不同格式的参数
        self.params_stack = QStackedWidget()

        # ONNX参数页面
        onnx_widget = self.create_onnx_params_widget()
        self.params_stack.addWidget(onnx_widget)

        # TensorRT参数页面
        tensorrt_widget = self.create_tensorrt_params_widget()
        self.params_stack.addWidget(tensorrt_widget)

        # CoreML参数页面
        coreml_widget = self.create_coreml_params_widget()
        self.params_stack.addWidget(coreml_widget)

        # TensorFlow Lite参数页面
        tflite_widget = self.create_tflite_params_widget()
        self.params_stack.addWidget(tflite_widget)

        layout.addWidget(self.params_stack)

        # 通用参数
        common_layout = QHBoxLayout()

        # 图像尺寸
        common_layout.addWidget(QLabel(self.get_str('imageSize')))
        self.image_size_spin = QSpinBox()
        self.image_size_spin.setRange(320, 1280)
        self.image_size_spin.setValue(640)
        self.image_size_spin.setSingleStep(32)
        common_layout.addWidget(self.image_size_spin)

        # 设备选择
        common_layout.addWidget(QLabel(self.get_str('device')))
        self.device_combo = QComboBox()
        self.device_combo.addItems(["cpu", "cuda:0"])
        common_layout.addWidget(self.device_combo)

        common_layout.addStretch()
        layout.addLayout(common_layout)

        return group

    def create_onnx_params_widget(self):
        """创建ONNX参数配置窗口"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # Opset版本
        self.onnx_opset_spin = QSpinBox()
        self.onnx_opset_spin.setRange(9, 17)
        self.onnx_opset_spin.setValue(12)
        layout.addRow(self.get_str('onnxOpset'), self.onnx_opset_spin)

        # 动态batch
        self.onnx_dynamic_check = QCheckBox(self.get_str('onnxDynamic'))
        layout.addRow("", self.onnx_dynamic_check)

        # 简化模型
        self.onnx_simplify_check = QCheckBox(self.get_str('onnxSimplify'))
        self.onnx_simplify_check.setChecked(True)
        layout.addRow("", self.onnx_simplify_check)

        return widget

    def create_tensorrt_params_widget(self):
        """创建TensorRT参数配置窗口"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # 精度模式
        self.tensorrt_precision_combo = QComboBox()
        self.tensorrt_precision_combo.addItems(["fp16", "fp32"])
        layout.addRow(self.get_str('tensorrtPrecision'), self.tensorrt_precision_combo)

        # 工作空间大小
        self.tensorrt_workspace_spin = QSpinBox()
        self.tensorrt_workspace_spin.setRange(1, 16)
        self.tensorrt_workspace_spin.setValue(4)
        self.tensorrt_workspace_spin.setSuffix(" GB")
        layout.addRow(self.get_str('tensorrtWorkspace'), self.tensorrt_workspace_spin)

        return widget

    def create_coreml_params_widget(self):
        """创建CoreML参数配置窗口"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # CoreML特定参数可以在这里添加
        info_label = QLabel(self.get_str('coremlInfo'))
        info_label.setWordWrap(True)
        layout.addRow(info_label)

        return widget

    def create_tflite_params_widget(self):
        """创建TensorFlow Lite参数配置窗口"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # TensorFlow Lite特定参数可以在这里添加
        info_label = QLabel(self.get_str('tfliteInfo'))
        info_label.setWordWrap(True)
        layout.addRow(info_label)

        return widget

    def create_output_group(self):
        """创建输出设置区域"""
        group = QGroupBox(self.get_str('outputSettings'))
        layout = QVBoxLayout(group)

        # 输出目录
        dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText(self.get_str('selectOutputDir'))
        self.browse_output_btn = QPushButton(self.get_str('browse'))
        self.browse_output_btn.clicked.connect(self.browse_output_dir)

        dir_layout.addWidget(QLabel(self.get_str('outputDir')))
        dir_layout.addWidget(self.output_dir_edit, 1)
        dir_layout.addWidget(self.browse_output_btn)
        layout.addLayout(dir_layout)

        # 输出文件名
        name_layout = QHBoxLayout()
        self.output_name_edit = QLineEdit()
        self.output_name_edit.setPlaceholderText(self.get_str('outputFileName'))

        name_layout.addWidget(QLabel(self.get_str('fileName')))
        name_layout.addWidget(self.output_name_edit, 1)
        layout.addLayout(name_layout)

        return group

    def create_progress_group(self):
        """创建进度显示区域"""
        self.progress_group = QGroupBox(self.get_str('exportProgress'))
        layout = QVBoxLayout(self.progress_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel(self.get_str('ready'))
        layout.addWidget(self.status_label)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)

        return self.progress_group

    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        layout.addStretch()

        # 导出按钮
        self.export_btn = QPushButton(self.get_str('startExport'))
        self.export_btn.setObjectName("primaryButton")
        self.export_btn.clicked.connect(self.start_export)
        layout.addWidget(self.export_btn)

        # 取消按钮
        self.cancel_btn = QPushButton(self.get_str('cancel'))
        self.cancel_btn.clicked.connect(self.cancel_export)
        layout.addWidget(self.cancel_btn)

        # 关闭按钮
        self.close_btn = QPushButton(self.get_str('close'))
        self.close_btn.clicked.connect(self.close)
        layout.addWidget(self.close_btn)

        return layout

    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #fafafa;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #1976d2;
                background-color: white;
            }

            #titleLabel {
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 10px;
            }

            #infoLabel, #descLabel {
                color: #666;
                font-style: italic;
                margin: 5px 0;
            }

            #primaryButton {
                background-color: #1976d2;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }

            #primaryButton:hover {
                background-color: #1565c0;
            }

            #primaryButton:disabled {
                background-color: #ccc;
            }

            QPushButton {
                padding: 6px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }

            QPushButton:hover {
                background-color: #f5f5f5;
                border-color: #1976d2;
            }

            QLineEdit, QComboBox, QSpinBox {
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }

            QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
                border-color: #1976d2;
                outline: none;
            }

            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f9f9f9;
                font-family: monospace;
                font-size: 12px;
            }

            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
                background-color: #f0f0f0;
            }

            QProgressBar::chunk {
                background-color: #4caf50;
                border-radius: 3px;
            }
        """)

    def load_settings(self):
        """加载设置"""
        # 加载上次的输出目录
        last_output_dir = self.settings.get(SETTING_MODEL_EXPORT_DIR, os.path.expanduser("~"))
        self.output_dir_edit.setText(last_output_dir)

        # 检测可用设备
        self.detect_available_devices()

    def detect_available_devices(self):
        """检测可用设备"""
        devices = ["cpu"]

        try:
            import torch
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    devices.append(f"cuda:{i}")
        except ImportError:
            pass

        self.device_combo.clear()
        self.device_combo.addItems(devices)

    def browse_model_file(self):
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.get_str('selectModelFile'),
            "",
            "YOLO Models (*.pt *.onnx *.engine);;All Files (*)"
        )

        if file_path:
            self.model_path_edit.setText(file_path)
            self.update_model_info(file_path)

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = QFileDialog.getExistingDirectory(
            self,
            self.get_str('selectOutputDir'),
            self.output_dir_edit.text() or os.path.expanduser("~")
        )

        if directory:
            self.output_dir_edit.setText(directory)
            # 保存到设置
            self.settings[SETTING_MODEL_EXPORT_DIR] = directory
            self.settings.save()

    def update_model_info(self, model_path):
        """更新模型信息显示"""
        try:
            if not os.path.exists(model_path):
                self.model_info_label.setText(self.get_str('modelFileNotFound'))
                return

            # 获取文件大小
            file_size = os.path.getsize(model_path)
            size_mb = file_size / (1024 * 1024)

            # 获取文件名
            file_name = os.path.basename(model_path)

            info_text = f"{self.get_str('modelFile')}: {file_name}\n"
            info_text += f"{self.get_str('fileSize')}: {size_mb:.1f} MB"

            self.model_info_label.setText(info_text)

            # 自动设置输出文件名
            if not self.output_name_edit.text():
                base_name = os.path.splitext(file_name)[0]
                self.output_name_edit.setText(f"{base_name}_exported")

        except Exception as e:
            self.model_info_label.setText(f"{self.get_str('modelInfoError')}: {str(e)}")

    def on_format_changed(self, format_text):
        """格式改变事件"""
        format_map = {
            "ONNX (.onnx)": (0, self.get_str('onnxDescription')),
            "TensorRT (.engine)": (1, self.get_str('tensorrtDescription')),
            "CoreML (.mlmodel)": (2, self.get_str('coremlDescription')),
            "TensorFlow Lite (.tflite)": (3, self.get_str('tfliteDescription'))
        }

        if format_text in format_map:
            index, description = format_map[format_text]
            self.params_stack.setCurrentIndex(index)
            self.format_desc_label.setText(description)

    def validate_inputs(self):
        """验证输入"""
        # 检查模型文件
        model_path = self.model_path_edit.text().strip()
        if not model_path:
            QMessageBox.warning(self, self.get_str('warning'), self.get_str('pleaseSelectModel'))
            return False

        if not os.path.exists(model_path):
            QMessageBox.warning(self, self.get_str('warning'), self.get_str('modelFileNotFound'))
            return False

        # 检查输出目录
        output_dir = self.output_dir_edit.text().strip()
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                QMessageBox.warning(self, self.get_str('warning'),
                                  f"{self.get_str('createDirFailed')}: {str(e)}")
                return False

        # 检查输出文件名
        output_name = self.output_name_edit.text().strip()
        if not output_name:
            QMessageBox.warning(self, self.get_str('warning'), self.get_str('pleaseEnterFileName'))
            return False

        return True

    def get_export_config(self):
        """获取导出配置"""
        config = ExportConfig()

        # 基本设置
        config.model_path = self.model_path_edit.text().strip()
        config.output_dir = self.output_dir_edit.text().strip()
        config.output_name = self.output_name_edit.text().strip()

        # 格式设置
        format_text = self.format_combo.currentText()
        if "ONNX" in format_text:
            config.export_format = "onnx"
        elif "TensorRT" in format_text:
            config.export_format = "tensorrt"
        elif "CoreML" in format_text:
            config.export_format = "coreml"
        elif "TensorFlow Lite" in format_text:
            config.export_format = "tflite"

        # 通用参数
        config.image_size = self.image_size_spin.value()
        config.device = self.device_combo.currentText()

        # 格式特定参数
        if config.export_format == "onnx":
            config.onnx_opset = self.onnx_opset_spin.value()
            config.onnx_dynamic = self.onnx_dynamic_check.isChecked()
            config.onnx_simplify = self.onnx_simplify_check.isChecked()
        elif config.export_format == "tensorrt":
            config.tensorrt_precision = self.tensorrt_precision_combo.currentText()
            config.tensorrt_workspace = self.tensorrt_workspace_spin.value()

        return config

    def start_export(self):
        """开始导出"""
        if not self.validate_inputs():
            return

        # 获取导出配置
        config = self.get_export_config()

        # 禁用控件
        self.export_btn.setEnabled(False)
        self.browse_model_btn.setEnabled(False)
        self.browse_output_btn.setEnabled(False)
        self.model_path_edit.setEnabled(False)
        self.output_dir_edit.setEnabled(False)
        self.output_name_edit.setEnabled(False)
        self.format_combo.setEnabled(False)

        # 显示进度区域
        self.progress_group.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        self.status_label.setText(self.get_str('preparingExport'))

        # 创建并启动导出线程
        self.export_thread = ModelExportThread(config)
        self.export_thread.progress_updated.connect(self.on_progress_updated)
        self.export_thread.log_message.connect(self.on_log_message)
        self.export_thread.export_completed.connect(self.on_export_completed)
        self.export_thread.start()

    def cancel_export(self):
        """取消导出"""
        if self.export_thread and self.export_thread.isRunning():
            self.export_thread.cancel()
            self.export_thread.wait(3000)  # 等待3秒

            if self.export_thread.isRunning():
                self.export_thread.terminate()
                self.export_thread.wait()

            self.on_export_completed(False, self.get_str('exportCancelled'))
        else:
            self.close()

    def on_progress_updated(self, value, message):
        """进度更新"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def on_log_message(self, message):
        """日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def on_export_completed(self, success, message):
        """导出完成"""
        # 恢复控件状态
        self.export_btn.setEnabled(True)
        self.browse_model_btn.setEnabled(True)
        self.browse_output_btn.setEnabled(True)
        self.model_path_edit.setEnabled(True)
        self.output_dir_edit.setEnabled(True)
        self.output_name_edit.setEnabled(True)
        self.format_combo.setEnabled(True)

        if success:
            self.status_label.setText(self.get_str('exportComplete'))
            QMessageBox.information(self, self.get_str('success'), message)
        else:
            self.status_label.setText(self.get_str('exportFailed'))
            QMessageBox.critical(self, self.get_str('error'), message)

        # 清理线程
        if self.export_thread:
            self.export_thread.deleteLater()
            self.export_thread = None

    def closeEvent(self, event):
        """关闭事件"""
        if self.export_thread and self.export_thread.isRunning():
            reply = QMessageBox.question(
                self,
                self.get_str('confirmClose'),
                self.get_str('exportInProgress'),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.export_thread.cancel()
                self.export_thread.wait(3000)
                if self.export_thread.isRunning():
                    self.export_thread.terminate()
                    self.export_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
