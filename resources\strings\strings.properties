openFile=Open
openFileDetail=Open image or label file
quit=Quit
quitApp=Quit application
openDir=Open Dir
copyPrevBounding=Copy previous Bounding Boxes in the current image
changeSavedAnnotationDir=Change default saved Annotation dir
openAnnotation=Open Annotation
openAnnotationDetail=Open an annotation file
changeSaveDir=Change Save Dir
nextImg=Next Image
nextImgDetail=Open the next Image
prevImg=Prev Image
prevImgDetail=Open the previous Image
verifyImg=Verify Image
verifyImgDetail=Verify Image
save=Save
saveDetail=Save the labels to a file
changeSaveFormat=Change save format
saveAs=Save As
saveAsDetail=Save the labels to a different file
closeCur=Close
closeCurDetail=Close the current file
deleteImg=Delete current image
deleteImgDetail=Delete the current image
resetAll=Reset All
resetAllDetail=Reset All
boxLineColor=Box Line Color
boxLineColorDetail=Choose Box line color
crtBox=Create RectBox
crtBoxDetail=Draw a new box
delBox=Delete RectBox
delBoxDetail=Remove the box
dupBox=Duplicate RectBox
dupBoxDetail=Create a duplicate of the selected box
editBox=&Edit RectBox
editBoxDetail=Move and edit Boxs
hideAllBox=&Hide RectBox
showAllBox=&Show RectBox
tutorialDefault=Tutorial
tutorialChrome=Tutorial(Chrome)
tutorialDetail=Show demo
info=Information
shortcut=Keyboard shortcuts
zoomin=Zoom In
zoominDetail=Increase zoom level
zoomout=Zoom Out
zoomoutDetail=Decrease zoom level
originalsize=Original size
originalsizeDetail=Zoom to original size
fitWin=Fit Window
fitWinDetail=Zoom follows window size
fitWidth=Fit Width
fitWidthDetail=Zoom follows window width
lightbrighten=Brighten
lightbrightenDetail=Increase Image Brightness
lightdarken=Darken
lightdarkenDetail=Decrease Image Brightness
lightreset=Original Brightness
lightresetDetail=Restore original brightness
lightWidgetTitle=Image Brightness
editLabel=Edit Label
editLabelDetail=Modify the label of the selected Box
shapeLineColor=Shape Line Color
shapeLineColorDetail=Change the line color for this specific shape
shapeFillColor=Shape Fill Color
shapeFillColorDetail=Change the fill color for this specific shape
showHide=Show/Hide Label Panel
useDefaultLabel=Use default label
useDifficult=difficult
boxLabelText=Box Labels
labels=Labels
autoSaveMode=Auto Save mode
singleClsMode=Single Class Mode
displayLabel=Display Labels
fileList=File List
files=Files
advancedMode=Advanced Mode
advancedModeDetail=Swtich to advanced mode
showAllBoxDetail=Show all bounding boxes
hideAllBoxDetail=Hide all bounding boxes
menu_file=&File
menu_edit=&Edit
menu_view=&View
menu_help=&Help
menu_openRecent=Open &Recent
chooseLineColor=Choose Line Color
chooseFillColor=Choose Fill Color
drawSquares=Draw Squares
exportYOLO=Export as YOLO Dataset
exportYOLODetail=Export Pascal VOC annotations as YOLO format dataset
exportYOLODialog=Export YOLO Dataset
selectExportDir=Select Export Directory
datasetName=Dataset Name
trainRatio=Training Set Ratio
exportProgress=Export Progress
exportComplete=Export Complete
exportSuccess=YOLO dataset exported successfully!
exportError=Export Failed
noAnnotations=No annotation files found
invalidDirectory=Invalid directory path
processingFiles=Processing files...
copyingImages=Copying images...
generatingConfig=Generating configuration files...
exportCancelled=Export cancelled

# Model Export Dialog
exportModel=Export Model
exportModelDetail=Export YOLO model to other formats (ONNX, TensorRT, etc.)
exportModelDialog=Export Model
exportModelTitle=Export YOLO Model to Other Formats
selectModel=Select Model
selectModelPath=Select YOLO model file (.pt)
modelPath=Model Path:
browse=Browse...
noModelSelected=No model selected
modelFile=Model File
fileSize=File Size
modelInfoError=Model info error
modelFileNotFound=Model file not found
selectModelFile=Select Model File

exportFormat=Export Format
format=Format:
onnxDescription=ONNX format for cross-platform inference
tensorrtDescription=TensorRT format optimized for NVIDIA GPUs
coremlDescription=CoreML format for Apple devices
tfliteDescription=TensorFlow Lite format for mobile devices

exportParameters=Export Parameters
imageSize=Image Size:
device=Device:
onnxOpset=ONNX Opset:
onnxDynamic=Dynamic batch size
onnxSimplify=Simplify model
tensorrtPrecision=Precision:
tensorrtWorkspace=Workspace:
coremlInfo=CoreML export with default settings
tfliteInfo=TensorFlow Lite export with default settings

outputSettings=Output Settings
outputDir=Output Directory:
selectOutputDir=Select output directory
fileName=File Name:
outputFileName=Enter output file name

startExport=Start Export
cancel=Cancel
close=Close
ready=Ready
preparingExport=Preparing export...
exportInProgress=Export is in progress. Are you sure you want to close?
confirmClose=Confirm Close
success=Success
error=Error
warning=Warning
pleaseSelectModel=Please select a model file
pleaseEnterFileName=Please enter a file name
createDirFailed=Failed to create directory
exportFailed=Export Failed

# Additional strings for model export
onnxDynamic=Dynamic batch size
onnxSimplify=Simplify model
tensorrtPrecision=Precision:
tensorrtWorkspace=Workspace:
coremlInfo=CoreML export with default settings
tfliteInfo=TensorFlow Lite export with default settings
imageSize=Image Size:
device=Device:
outputSettings=Output Settings
selectOutputDir=Select output directory
outputDir=Output Directory:
outputFileName=Enter output file name
fileName=File Name:
ready=Ready
startExport=Start Export
cancel=Cancel
close=Close
selectModelFile=Select Model File