openFile=打开文件
openFileDetail=打开图像文件
quit=退出
quitApp=退出程序
openDir=打开目录
copyPrevBounding=复制当前图像中的上一个边界框
changeSavedAnnotationDir=更改保存标签文件的预设目录
openAnnotation=开启标签
openAnnotationDetail=打开标签文件
changeSaveDir=改变存放目录
nextImg=下一个图像
nextImgDetail=下一个图像
prevImg=上一个图像
prevImgDetail=上一个图像
verifyImg=验证图像
verifyImgDetail=验证图像
save=保存
saveDetail=保存标签文件
changeSaveFormat=更改存储格式
saveAs=另存为
saveAsDetail=將标签保存到其他文件
closeCur=关闭文件
closeCurDetail=关闭当前文件
deleteImg=删除图像
deleteImgDetail=删除当前图像
resetAll=全部重置
resetAllDetail=重置所有设定
boxLineColor=区块线条颜色
boxLineColorDetail=选择线框颜色
crtBox=创建区块
crtBoxDetail=创建一个新的区块
delBox=删除选择的区块
delBoxDetail=删除区块
dupBox=复制区块
dupBoxDetail=复制区块
editBox=编辑区块
editBoxDetail=编辑区块
hideAllBox=隐藏所有区块
showAllBox=显示所有区块
tutorialDefault=YouTube教学
tutorialChrome=YouTube教学(Chrome)
tutorialDetail=显示示范内容
info=版本信息
shortcut=快捷键
zoomin=放大画面
zoominDetail=放大画面
zoomout=缩小画面
zoomoutDetail=缩小画面
originalsize=原始大小
originalsizeDetail=放大到原始大小
fitWin=调整到窗口大小
fitWinDetail=缩放到当前窗口大小
fitWidth=缩放到跟当前画面一样宽
fitWidthDetail=调整宽度适应到窗口宽度
lightbrighten=提亮
lightbrightenDetail=增加图像亮度
lightdarken=变暗
lightdarkenDetail=降低图像亮度
lightreset=原始亮度
lightresetDetail=恢复原来的亮度
lightWidgetTitle=图像亮度
editLabel=编辑标签
editLabelDetail=修改当前所选的区块颜色
shapeLineColor=形状线条颜色
shapeLineColorDetail=更改线条颜色
shapeFillColor=填充颜色
shapeFillColorDetail=更改填充颜色
showHide=显示/隐藏标签
useDefaultLabel=使用预设标签
useDifficult=有难度的
boxLabelText=区块的标签
labels=标签
autoSaveMode=自动保存模式
singleClsMode=单一类别模式
displayLabel=显示类别
fileList=文件列表
files=文件
advancedMode=专家模式
advancedModeDetail=切换到专家模式
showAllBoxDetail=显示所有区块
hideAllBoxDetail=隐藏所有区块
menu_file=文件(&F)
menu_edit=编辑(&E)
menu_view=查看(&V)
menu_help=帮助(&H)
menu_openRecent=最近打开(&R)
chooseLineColor=选择线条颜色
chooseFillColor=选择填充颜色
drawSquares=绘制正方形
exportYOLO=导出为YOLO数据集
exportYOLODetail=将Pascal VOC标注导出为YOLO格式数据集
exportYOLODialog=导出YOLO数据集
selectExportDir=选择导出目录
datasetName=数据集名称
trainRatio=训练集比例
exportProgress=导出进度
exportComplete=导出完成
exportSuccess=YOLO数据集导出成功！
exportError=导出失败
noAnnotations=未找到标注文件
invalidDirectory=无效的目录路径
processingFiles=正在处理文件...
copyingImages=正在复制图片...
generatingConfig=正在生成配置文件...
exportCancelled=导出已取消

# 模型导出对话框
exportModel=导出模型
exportModelDetail=将YOLO模型导出为其他格式（ONNX、TensorRT等）
exportModelDialog=导出模型
exportModelTitle=导出YOLO模型为其他格式
selectModel=选择模型
selectModelPath=选择YOLO模型文件 (.pt)
modelPath=模型路径：
browse=浏览...
noModelSelected=未选择模型
modelFile=模型文件
fileSize=文件大小
modelInfoError=模型信息错误
modelFileNotFound=模型文件不存在
selectModelFile=选择模型文件

exportFormat=导出格式
format=格式：
onnxDescription=ONNX格式，用于跨平台推理
tensorrtDescription=TensorRT格式，针对NVIDIA GPU优化
coremlDescription=CoreML格式，用于Apple设备
tfliteDescription=TensorFlow Lite格式，用于移动设备

exportParameters=导出参数
imageSize=图像尺寸：
device=设备：
onnxOpset=ONNX Opset：
onnxDynamic=动态批次大小
onnxSimplify=简化模型
tensorrtPrecision=精度：
tensorrtWorkspace=工作空间：
coremlInfo=使用默认设置导出CoreML
tfliteInfo=使用默认设置导出TensorFlow Lite

outputSettings=输出设置
outputDir=输出目录：
selectOutputDir=选择输出目录
fileName=文件名：
outputFileName=输入输出文件名

startExport=开始导出
cancel=取消
close=关闭
ready=就绪
preparingExport=准备导出...
exportInProgress=导出正在进行中，确定要关闭吗？
confirmClose=确认关闭
success=成功
error=错误
warning=警告
pleaseSelectModel=请选择模型文件
pleaseEnterFileName=请输入文件名
createDirFailed=创建目录失败
exportFailed=导出失败

# 模型导出附加字符串
onnxDynamic=动态批次大小
onnxSimplify=简化模型
tensorrtPrecision=精度：
tensorrtWorkspace=工作空间：
coremlInfo=使用默认设置导出CoreML
tfliteInfo=使用默认设置导出TensorFlow Lite
imageSize=图像尺寸：
device=设备：
outputSettings=输出设置
selectOutputDir=选择输出目录
outputDir=输出目录：
outputFileName=输入输出文件名
fileName=文件名：
ready=就绪
startExport=开始导出
cancel=取消
close=关闭
selectModelFile=选择模型文件
